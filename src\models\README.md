# Models - Unified MySQL & MySQL2

Thư mục này chứa các model đã đư<PERSON>c gộp chung để hỗ trợ cả MySQL (Sequelize) và MySQL2.

## Cấu trúc

```
src/models/
├── BaseModel.js      # Base model gộp chung
├── helpers.js        # Helpers gộp chung (Enums, Fields, Queries, Utils)
├── index.js          # Index file gộp chung
├── mysql/            # Models cho MySQL (Sequelize)
├── mysql2/           # Models cho MySQL2
└── README.md         # File này
```

## Sử dụng

### 1. Import các thành phần cần thiết

```javascript
import {
  BaseModel,
  Enums,
  Fields,
  Queries,
  Utils,
  initializeMySQLModels,
  initializeMySQL2Models,
  initializeAllModels
} from './models/index.js';
```

### 2. Sử dụng với MySQL (Sequelize)

```javascript
import { Sequelize } from 'sequelize';

// Tạo Sequelize instance
const sequelize = new Sequelize(/* config */);

// Initialize MySQL models
const mysqlModels = initializeMySQLModels(sequelize);

// Sử dụng models
const transaction = await mysqlModels.Transaction.create({
  accountId: 'user123',
  amount: 100.50,
  status: Enums.STATUS.PENDING
});
```

### 3. Sử dụng với MySQL2

```javascript
// Initialize MySQL2 models
const mysql2Models = initializeMySQL2Models();

// Sử dụng models (chỉ là class definitions)
const userFields = mysql2Models.UserModel.getFields();
const userTableName = mysql2Models.UserModel.tableName;
```

### 4. Sử dụng cả hai

```javascript
// Initialize tất cả models
const allModels = initializeAllModels(sequelize);

// Truy cập MySQL models
const mysqlTransaction = allModels.mysql.Transaction;

// Truy cập MySQL2 models
const mysql2User = allModels.mysql2.UserModel;
```

## BaseModel

BaseModel đã được gộp chung để hỗ trợ cả hai loại database:

### Cho Sequelize (MySQL)
```javascript
class MyModel extends BaseModel {
  static tableName = 'my_table';
  
  static getFields(DataTypes) {
    return {
      ...super.getFields(DataTypes),
      name: {
        type: DataTypes.STRING,
        allowNull: false
      }
    };
  }
}
```

### Cho MySQL2
```javascript
class MyModel extends BaseModel {
  static get tableName() {
    return 'my_table';
  }
  
  static getFields() {
    return [
      ...super.getBaseFields(),
      'name',
      'description'
    ];
  }
}
```

## Helpers

### Enums
Chứa tất cả các enum từ cả MySQL và MySQL2:
```javascript
// Từ MySQL
Enums.STATUS.PENDING
Enums.PAYMENT_METHOD.BANK

// Từ MySQL2
Enums.USER_STATUS.ACTIVE
Enums.GENDER.MALE
```

### Fields
Chứa các field definition cho cả hai loại:
```javascript
// Sequelize field
const accountIdField = Fields.accountId(DataTypes);

// MySQL2 field
const userIdField = Fields.userId();
```

### Queries
Các query pattern chung:
```javascript
// Sequelize queries
const todayQuery = Queries.today();
const statusQuery = Queries.byStatus(1);

// MySQL2 queries
const userQuery = Queries.byUserId(123);
const ipQuery = Queries.byIP('***********');
```

### Utils
Các utility functions:
```javascript
// Kiểm tra loại database
const isSequelize = Utils.isSequelize(DataTypes);

// Convert queries
const mysql2Query = Utils.convertSequelizeToMySQL2(sequelizeQuery);
const sequelizeQuery = Utils.convertMySQL2ToSequelize(mysql2Query);

// Validation
const isValidEmail = Utils.isValidEmail('<EMAIL>');
```

## Migration từ code cũ

### Từ MySQL models
```javascript
// Cũ
import BaseModel from './mysql/BaseModel.js';
import { Enums } from './mysql/helpers.js';

// Mới
import { BaseModel, Enums } from './models/index.js';
```

### Từ MySQL2 models
```javascript
// Cũ
import BaseModel from './mysql2/BaseModel.js';
import { Enums } from './mysql2/helpers.js';

// Mới
import { BaseModel, Enums } from './models/index.js';
```

## Testing

```javascript
// Test connection và models
const testResult = await testConnection(sequelize);

// Test chỉ MySQL2
const mysql2Models = initializeMySQL2Models();
const isValid = validateAssociations(mysql2Models, 'mysql2');
```

## Lưu ý

1. **BaseModel**: Tự động detect loại database dựa trên tham số DataTypes
2. **Fields**: Hỗ trợ cả Sequelize DataTypes và raw SQL definitions
3. **Queries**: Có thể convert qua lại giữa Sequelize và MySQL2 format
4. **Utils**: Cung cấp các helper functions để làm việc với cả hai loại
5. **Backward compatibility**: Code cũ vẫn hoạt động bình thường

## Ví dụ hoàn chỉnh

```javascript
import { Sequelize } from 'sequelize';
import {
  initializeAllModels,
  Enums,
  Queries,
  Utils,
  testConnection
} from './models/index.js';

async function main() {
  // Setup Sequelize
  const sequelize = new Sequelize(/* config */);
  
  // Initialize all models
  const models = initializeAllModels(sequelize);
  
  // Test connections
  await testConnection(sequelize);
  
  // Use MySQL models
  const transaction = await models.mysql.Transaction.create({
    accountId: 'user123',
    amount: 100,
    status: Enums.STATUS.PENDING
  });
  
  // Use MySQL2 models (for schema definition)
  const userFields = models.mysql2.UserModel.getFields();
  
  console.log('✅ All models working correctly!');
}

main().catch(console.error);
```
