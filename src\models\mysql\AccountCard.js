import BaseModel from '../BaseModel.js';
import { CommonFields, CommonScopes } from './ModelUtils.js';
import { Op } from 'sequelize';

/**
 * AccountCard Model
 * Represents account card information
 */
export default class AccountCard extends BaseModel {
  static get tableName() {
    return 'account_cards';
  }

  static getAttributes(DataTypes) {
    return Object.assign({}, super.getAttributes(DataTypes), {
      // Account information
      accountId: CommonFields.accountId(DataTypes),
      accountName: CommonFields.accountName(DataTypes),

      // Card information
      cardNumber: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: 'card_number',
        comment: 'Card number',
      },
      cardType: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 0,
        field: 'card_type',
        comment: 'Card type: 0=Unknown, 1=Credit, 2=Debit, 3=Prepaid',
        validate: {
          isIn: [[0, 1, 2, 3]],
        },
      },
      cardHolder: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: 'card_holder',
        comment: 'Card holder name',
      },
      expiryDate: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'expiry_date',
        comment: 'Card expiry date',
      },

      // Bank information
      bankName: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: 'bank_name',
        comment: 'Bank name',
      },
      bankCode: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: 'bank_code',
        comment: 'Bank code',
      },

      // Status
      status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: 'Card status: 0=Inactive, 1=Active, 2=Suspended, 3=Expired',
        validate: {
          isIn: [[0, 1, 2, 3]],
        },
      },

      // Additional data
      metaData: CommonFields.metaData(DataTypes),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        {
          fields: ['account_id'],
        },
        {
          fields: ['card_number'],
          unique: true,
        },
        {
          fields: ['card_type'],
        },
        {
          fields: ['status'],
        },
        {
          fields: ['bank_code'],
        },
        {
          fields: ['expiry_date'],
        },
      ],
    });
  }

  /**
   * Get card type enum
   * @returns {Object} Card type enum
   */
  static getCardTypeEnum() {
    return {
      UNKNOWN: 0,
      CREDIT: 1,
      DEBIT: 2,
      PREPAID: 3,
    };
  }

  /**
   * Get card status enum
   * @returns {Object} Status enum
   */
  static getStatusEnum() {
    return {
      INACTIVE: 0,
      ACTIVE: 1,
      SUSPENDED: 2,
      EXPIRED: 3,
    };
  }

  // Scope methods
  static scopeActive() {
    return { where: { status: this.getStatusEnum().ACTIVE } };
  }

  static scopeInactive() {
    return { where: { status: this.getStatusEnum().INACTIVE } };
  }

  static scopeSuspended() {
    return { where: { status: this.getStatusEnum().SUSPENDED } };
  }

  static scopeExpired() {
    return { where: { status: this.getStatusEnum().EXPIRED } };
  }

  static scopeByAccount(accountId) {
    return CommonScopes.byAccount(accountId);
  }

  static scopeByType(cardType) {
    return { where: { cardType } };
  }

  static scopeCreditCards() {
    return { where: { cardType: this.getCardTypeEnum().CREDIT } };
  }

  static scopeDebitCards() {
    return { where: { cardType: this.getCardTypeEnum().DEBIT } };
  }

  static scopePrepaidCards() {
    return { where: { cardType: this.getCardTypeEnum().PREPAID } };
  }

  static scopeByBank(bankCode) {
    return { where: { bankCode } };
  }

  static scopeValid() {
    return {
      where: {
        expiryDate: { [Op.gt]: new Date() },
      },
    };
  }

  static scopeExpiredCards() {
    return {
      where: {
        expiryDate: { [Op.lte]: new Date() },
      },
    };
  }
}
