import BaseModel from './BaseModel.js';
import { Enums, Fields, Utils } from './helpers.js';
import MySQL2Service from '../../services/mysql2Service.js';

/**
 * Model SignUpSource - Quản lý nguồn đăng ký người dùng
 * Chỉ chứa định nghĩa cấu trúc, logic query được tách ra service
 * Tuân theo pattern của MySQL models
 */
export default class SignUpSourceModel extends BaseModel {
  static get tableName() {
    return 'sign_up_source';
  }

  /**
   * Đ<PERSON>nh nghĩa các field của bảng sign_up_source
   */
  static getFields() {
    return Object.assign({}, super.getBaseFields(), {
      user_id: Fields.userId(),
      source: {
        type: 'TINYINT',
        allowNull: false,
        defaultValue: 0,
        comment: 'Nguồn đăng ký',
      },
      source_detail: {
        type: 'VARCHAR(255)',
        allowNull: true,
        field: 'source_detail',
        comment: 'Chi tiết nguồn',
      },
      referrer_url: {
        type: 'VARCHAR(500)',
        allowNull: true,
        field: 'referrer_url',
        comment: 'URL giới thiệu',
      },
      utm_source: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'utm_source',
        comment: 'UTM Source',
      },
      utm_medium: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'utm_medium',
        comment: 'UTM Medium',
      },
      utm_campaign: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'utm_campaign',
        comment: 'UTM Campaign',
      },
      utm_content: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'utm_content',
        comment: 'UTM Content',
      },
      utm_term: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'utm_term',
        comment: 'UTM Term',
      },
      create_time: Fields.createTime(),
    });
  }

  /**
   * Cấu hình tùy chọn
   */
  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['user_id'] },
        { fields: ['source'] },
        { fields: ['create_time'] },
        { fields: ['utm_source'] },
        { fields: ['utm_medium'] },
        { fields: ['utm_campaign'] },
        { fields: ['user_id', 'source'] },
        { fields: ['source', 'create_time'] },
      ],
    });
  }

  /**
   * Lấy enum source
   */
  static getSourceEnum() {
    return Enums.SIGNUP_SOURCE;
  }

  /**
   * Transform dữ liệu sign up source
   */
  static transform(row) {
    if (!row) return null;

    return {
      ...row,
      source_detail: Utils.sanitizeString(row.source_detail),
      referrer_url: Utils.sanitizeString(row.referrer_url),
      utm_source: Utils.sanitizeString(row.utm_source),
      utm_medium: Utils.sanitizeString(row.utm_medium),
      utm_campaign: Utils.sanitizeString(row.utm_campaign),
      utm_content: Utils.sanitizeString(row.utm_content),
      utm_term: Utils.sanitizeString(row.utm_term),
      source_name: SignUpSourceModel.getSourceName(row.source),
      createTimeFormatted: row.create_time ? Utils.timestampToDate(row.create_time) : null,
    };
  }

  /**
   * Lấy tên source từ enum
   */
  static getSourceName(sourceValue) {
    const sourceEnum = SignUpSourceModel.getSourceEnum();
    const entries = Object.entries(sourceEnum);
    for (const [key, value] of entries) {
      if (value === sourceValue) {
        return key;
      }
    }
    return 'UNKNOWN';
  }

  // === QUERY HELPERS (Static methods like MySQL models) ===

  /**
   * Đếm tổng số sign up source
   */
  static async estimatedDocumentCount() {
    return await MySQL2Service.estimatedDocumentCount(this.tableName);
  }

  /**
   * Đếm số sign up source theo điều kiện
   */
  static async countDocuments(conditions = {}) {
    return await MySQL2Service.countDocuments(this.tableName, conditions);
  }

  /**
   * Thống kê theo nguồn đăng ký (giữ lại method cũ để tương thích)
   * @param {number} startTime - Thời gian bắt đầu (timestamp)
   * @param {number} endTime - Thời gian kết thúc (timestamp)
   */
  static async getSignUpSourceStats(startTime, endTime) {
    const sql = `
      SELECT
        source,
        COUNT(*) as count,
        COUNT(DISTINCT user_id) as unique_users
      FROM ${this.tableName}
      WHERE create_time BETWEEN ? AND ?
      GROUP BY source
      ORDER BY count DESC
    `;
    const rows = await MySQL2Service.query(sql, [startTime, endTime]);

    return rows.map((row) => ({
      ...row,
      source_name: SignUpSourceModel.getSourceName(row.source),
      count: Number(row.count),
      unique_users: Number(row.unique_users),
    }));
  }

  /**
   * Tìm theo source
   */
  static async findBySource(source, options = {}) {
    return await MySQL2Service.find(this.tableName, { source }, options, this.transform);
  }

  /**
   * Tìm từ website
   */
  static async findFromWebsite(options = {}) {
    return await this.findBySource(Enums.SIGNUP_SOURCE.WEBSITE, options);
  }

  /**
   * Tìm từ mobile app
   */
  static async findFromMobileApp(options = {}) {
    return await this.findBySource(Enums.SIGNUP_SOURCE.MOBILE_APP, options);
  }

  /**
   * Tìm từ social media
   */
  static async findFromSocialMedia(options = {}) {
    return await this.findBySource(Enums.SIGNUP_SOURCE.SOCIAL_MEDIA, options);
  }

  /**
   * Tìm trong khoảng thời gian
   */
  static async findByTimeRange(startTime, endTime, options = {}) {
    return await MySQL2Service.findByTimeRange(
      this.tableName,
      startTime,
      endTime,
      options,
      this.transform
    );
  }

  /**
   * Đếm theo source
   */
  static async countBySource(source) {
    return await MySQL2Service.countDocuments(this.tableName, { source });
  }

  /**
   * Thống kê theo source (pattern mới)
   */
  static async getSourceStats() {
    const sql = `
      SELECT
        source,
        COUNT(*) as count,
        COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ${this.tableName} WHERE deleted_at IS NULL) as percentage
      FROM ${this.tableName}
      WHERE deleted_at IS NULL
      GROUP BY source
      ORDER BY count DESC
    `;

    const rows = await MySQL2Service.query(sql);

    return rows.map((row) => ({
      source: row.source,
      sourceName: SignUpSourceModel.getSourceName(row.source),
      count: Number(row.count),
      percentage: Number(row.percentage).toFixed(2),
    }));
  }
}
