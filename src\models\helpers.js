import { Op } from 'sequelize';

/**
 * <PERSON><PERSON><PERSON> giá trị enum chung - gộp từ cả MySQL và MySQL2
 */
export const Enums = {
  // Từ MySQL
  STATUS: {
    PENDING: 0,
    COMPLETED: 1,
    FAILED: 2,
    CANCELLED: 3,
  },
  PAYMENT_METHOD: {
    UNKNOWN: 0,
    BANK: 1,
    CARD: 2,
    WALLET: 3,
    CRYPTO: 4,
  },
  ACTION: {
    UNKNOWN: 0,
    LOGIN: 1,
    LOGOUT: 2,
    TRANSACTION: 3,
    PROFILE_UPDATE: 4,
    SECURITY_CHANGE: 5,
  },

  // Từ MySQL2
  USER_STATUS: {
    INACTIVE: 0,
    ACTIVE: 1,
    BANNED: 2,
    DELETED: 3,
  },
  PROFILE_STATUS: {
    INCOMPLETE: 0,
    COMPLETE: 1,
    VERIFIED: 2,
  },
  SIGNUP_SOURCE: {
    UNKNOWN: 0,
    WEBSITE: 1,
    MOBILE_APP: 2,
    SOCIAL_MEDIA: 3,
    REFERRAL: 4,
  },
  GENDER: {
    UNKNOWN: 0,
    MALE: 1,
    FEMALE: 2,
    OTHER: 3,
  },
  BOOLEAN: {
    FALSE: 0,
    TRUE: 1,
  },
};

/**
 * Các field definition chung - hỗ trợ cả Sequelize và MySQL2
 */
export const Fields = {
  // Sequelize fields (từ MySQL)
  accountId: (DataTypes) => ({
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'account_id',
    comment: 'ID tài khoản',
  }),

  accountName: (DataTypes) => ({
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'account_name',
    comment: 'Tên tài khoản',
  }),

  amount: (DataTypes) => ({
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Số tiền',
    validate: { min: 0 },
  }),

  coinAmount: (DataTypes) => ({
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    field: 'coin_amount',
    comment: 'Số coin',
    validate: { min: 0 },
  }),

  status: (DataTypes) => ({
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: 'Trạng thái',
    validate: { isIn: [[0, 1, 2, 3]] },
  }),

  paymentMethod: (DataTypes) => ({
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    field: 'payment_method',
    comment: 'Phương thức thanh toán',
    validate: { isIn: [[0, 1, 2, 3, 4]] },
  }),

  metaData: (DataTypes) => ({
    type: DataTypes.JSON,
    allowNull: true,
    field: 'meta_data',
    comment: 'Dữ liệu bổ sung',
  }),

  // MySQL2 fields (raw SQL definitions)
  id: () => ({
    type: 'INT',
    primaryKey: true,
    autoIncrement: true,
    comment: 'ID chính',
  }),

  userId: () => ({
    type: 'INT',
    allowNull: false,
    field: 'user_id',
    comment: 'ID người dùng',
  }),

  userName: () => ({
    type: 'VARCHAR(255)',
    allowNull: false,
    field: 'user_name',
    comment: 'Tên người dùng',
  }),

  email: () => ({
    type: 'VARCHAR(255)',
    allowNull: true,
    comment: 'Email',
  }),

  phone: () => ({
    type: 'VARCHAR(20)',
    allowNull: true,
    comment: 'Số điện thoại',
  }),

  ip: () => ({
    type: 'VARCHAR(45)',
    allowNull: true,
    comment: 'Địa chỉ IP',
  }),

  createdAt: () => ({
    type: 'TIMESTAMP',
    allowNull: false,
    defaultValue: 'CURRENT_TIMESTAMP',
    field: 'created_at',
    comment: 'Thời gian tạo',
  }),

  updatedAt: () => ({
    type: 'TIMESTAMP',
    allowNull: false,
    defaultValue: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    field: 'updated_at',
    comment: 'Thời gian cập nhật',
  }),

  deletedAt: () => ({
    type: 'TIMESTAMP',
    allowNull: true,
    field: 'deleted_at',
    comment: 'Thời gian xóa',
  }),

  createTime: () => ({
    type: 'BIGINT',
    allowNull: true,
    field: 'create_time',
    comment: 'Timestamp tạo',
  }),
};

/**
 * Các query pattern chung - hỗ trợ cả Sequelize và MySQL2
 */
export const Queries = {
  // Queries cho Sequelize (sử dụng Op)
  byAccount(accountId) {
    return { where: { accountId } };
  },

  byStatus(status) {
    return { where: { status } };
  },

  byDateRange(startDate, endDate) {
    return {
      where: {
        createdAt: { [Op.between]: [startDate, endDate] },
      },
    };
  },

  // Queries cho MySQL2 (sử dụng raw operators)
  byUserId(userId) {
    return {
      where: { user_id: userId },
    };
  },

  byIP(ip) {
    return {
      where: { ip },
    };
  },

  byTimeRange(startTime, endTime) {
    return {
      where: {
        createTime: {
          $gte: startTime,
          $lte: endTime,
        },
      },
    };
  },

  byDateRangeMySQL2(startDate, endDate) {
    return {
      where: {
        created_at: {
          $gte: startDate,
          $lte: endDate,
        },
      },
    };
  },

  // Common queries
  today() {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    return this.byDateRange(startOfDay, endOfDay);
  },

  thisMonth() {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    return this.byDateRange(startOfMonth, endOfMonth);
  },

  thisWeek() {
    const today = new Date();
    const startOfWeek = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - today.getDay()
    );
    const endOfWeek = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - today.getDay() + 6
    );
    return this.byDateRange(startOfWeek, endOfWeek);
  },

  withValidIP() {
    return {
      where: {
        ip: { $exists: true },
      },
    };
  },

  excludeIPs(ipList) {
    return {
      where: {
        ip: { $nin: ipList },
      },
    };
  },

  withPagination(page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    return {
      limit,
      offset,
    };
  },

  withOrderBy(field, direction = 'DESC') {
    return {
      orderBy: `${field} ${direction}`,
    };
  },

  activeUsers() {
    return this.byStatus(Enums.USER_STATUS.ACTIVE);
  },

  recentUsers(days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    return this.byDateRange(startDate, new Date());
  },
};

/**
 * Các utility functions
 */
export const Utils = {
  // Chuyển đổi timestamp sang Date
  timestampToDate(timestamp) {
    return new Date(timestamp * 1000);
  },

  // Chuyển đổi Date sang timestamp
  dateToTimestamp(date) {
    return Math.floor(date.getTime() / 1000);
  },

  // Format IP address
  formatIP(ip) {
    if (!ip) return null;
    return ip.trim();
  },

  // Validate email
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate phone
  isValidPhone(phone) {
    const phoneRegex = /^[+]?[\d\s-()]+$/;
    return phoneRegex.test(phone);
  },

  // Sanitize string
  sanitizeString(str) {
    if (!str) return null;
    return str.trim().replace(/[<>]/g, '');
  },

  // Build search conditions
  buildSearchConditions(searchTerm, fields) {
    if (!searchTerm || !fields.length) return {};

    const conditions = [];
    fields.forEach((field) => {
      conditions.push({
        [field]: { $like: `%${searchTerm}%` },
      });
    });

    return { $or: conditions };
  },

  // Merge query conditions
  mergeConditions(...conditionObjects) {
    const merged = {};
    conditionObjects.forEach((obj) => {
      if (obj && obj.where) {
        Object.assign(merged, obj.where);
      }
    });
    return { where: merged };
  },

  // Check if using Sequelize or MySQL2
  isSequelize(DataTypes) {
    return !!DataTypes;
  },

  // Get field definition based on database type
  getFieldDefinition(fieldName, DataTypes = null) {
    const field = Fields[fieldName];
    if (!field) {
      throw new Error(`Field ${fieldName} not found`);
    }
    return field(DataTypes);
  },

  // Convert Sequelize query to MySQL2 query
  convertSequelizeToMySQL2(query) {
    if (!query || !query.where) return query;

    const convertedWhere = {};
    Object.keys(query.where).forEach((key) => {
      const value = query.where[key];
      if (value && typeof value === 'object' && value[Op.between]) {
        convertedWhere[key] = {
          $gte: value[Op.between][0],
          $lte: value[Op.between][1],
        };
      } else {
        convertedWhere[key] = value;
      }
    });

    return { ...query, where: convertedWhere };
  },

  // Convert MySQL2 query to Sequelize query
  convertMySQL2ToSequelize(query) {
    if (!query || !query.where) return query;

    const convertedWhere = {};
    Object.keys(query.where).forEach((key) => {
      const value = query.where[key];
      if (value && typeof value === 'object') {
        if (value.$gte && value.$lte) {
          convertedWhere[key] = { [Op.between]: [value.$gte, value.$lte] };
        } else if (value.$like) {
          convertedWhere[key] = { [Op.like]: value.$like };
        } else if (value.$in) {
          convertedWhere[key] = { [Op.in]: value.$in };
        } else if (value.$nin) {
          convertedWhere[key] = { [Op.notIn]: value.$nin };
        } else {
          convertedWhere[key] = value;
        }
      } else {
        convertedWhere[key] = value;
      }
    });

    return { ...query, where: convertedWhere };
  },
};
