/**
 * <PERSON><PERSON><PERSON> giá trị enum chung cho MySQL2
 */
export const Enums = {
  USER_STATUS: {
    INACTIVE: 0,
    ACTIVE: 1,
    BANNED: 2,
    DELETED: 3,
  },

  PROFILE_STATUS: {
    INCOMPLETE: 0,
    COMPLETE: 1,
    VERIFIED: 2,
  },

  SIGNUP_SOURCE: {
    UNKNOWN: 0,
    WEBSITE: 1,
    MOBILE_APP: 2,
    SOCIAL_MEDIA: 3,
    REFERRAL: 4,
  },

  GENDER: {
    UNKNOWN: 0,
    MALE: 1,
    FEMALE: 2,
    OTHER: 3,
  },

  BOOLEAN: {
    FALSE: 0,
    TRUE: 1,
  },
};

/**
 * Các field definition chung
 */
export const Fields = {
  id: () => ({
    type: 'INT',
    primaryKey: true,
    autoIncrement: true,
    comment: 'ID chính',
  }),

  userId: () => ({
    type: 'INT',
    allowNull: false,
    field: 'user_id',
    comment: 'ID người dùng',
  }),

  userName: () => ({
    type: 'VARCHAR(255)',
    allowNull: false,
    field: 'user_name',
    comment: 'Tên người dùng',
  }),

  email: () => ({
    type: 'VARCHAR(255)',
    allowNull: true,
    comment: 'Email',
  }),

  phone: () => ({
    type: 'VARCHAR(20)',
    allowNull: true,
    comment: 'Số điện thoại',
  }),

  status: () => ({
    type: 'TINYINT',
    allowNull: false,
    defaultValue: 0,
    comment: 'Trạng thái',
  }),

  ip: () => ({
    type: 'VARCHAR(45)',
    allowNull: true,
    comment: 'Địa chỉ IP',
  }),

  createdAt: () => ({
    type: 'TIMESTAMP',
    allowNull: false,
    defaultValue: 'CURRENT_TIMESTAMP',
    field: 'created_at',
    comment: 'Thời gian tạo',
  }),

  updatedAt: () => ({
    type: 'TIMESTAMP',
    allowNull: false,
    defaultValue: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    field: 'updated_at',
    comment: 'Thời gian cập nhật',
  }),

  deletedAt: () => ({
    type: 'TIMESTAMP',
    allowNull: true,
    field: 'deleted_at',
    comment: 'Thời gian xóa',
  }),

  createTime: () => ({
    type: 'BIGINT',
    allowNull: true,
    field: 'create_time',
    comment: 'Timestamp tạo',
  }),
};

/**
 * Các query pattern chung
 */
export const Queries = {
  // Query theo user ID
  byUserId(userId) {
    return {
      where: { user_id: userId },
    };
  },

  // Query theo status
  byStatus(status) {
    return {
      where: { status },
    };
  },

  // Query theo IP
  byIP(ip) {
    return {
      where: { ip },
    };
  },

  // Query theo khoảng thời gian (createTime)
  byTimeRange(startTime, endTime) {
    return {
      where: {
        createTime: {
          $gte: startTime,
          $lte: endTime,
        },
      },
    };
  },

  // Query theo khoảng thời gian (created_at)
  byDateRange(startDate, endDate) {
    return {
      where: {
        created_at: {
          $gte: startDate,
          $lte: endDate,
        },
      },
    };
  },

  // Query hôm nay
  today() {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    return this.byDateRange(startOfDay, endOfDay);
  },

  // Query tháng này
  thisMonth() {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    return this.byDateRange(startOfMonth, endOfMonth);
  },

  // Query tuần này
  thisWeek() {
    const today = new Date();
    const startOfWeek = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - today.getDay()
    );
    const endOfWeek = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - today.getDay() + 6
    );
    return this.byDateRange(startOfWeek, endOfWeek);
  },

  // Query với IP không null
  withValidIP() {
    return {
      where: {
        ip: { $exists: true },
      },
    };
  },

  // Query với IP trong danh sách loại trừ
  excludeIPs(ipList) {
    return {
      where: {
        ip: { $nin: ipList },
      },
    };
  },

  // Query với limit và offset
  withPagination(page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    return {
      limit,
      offset,
    };
  },

  // Query với sắp xếp
  withOrderBy(field, direction = 'DESC') {
    return {
      orderBy: `${field} ${direction}`,
    };
  },

  // Query active users
  activeUsers() {
    return this.byStatus(Enums.USER_STATUS.ACTIVE);
  },

  // Query recent users (trong 30 ngày)
  recentUsers(days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    return this.byDateRange(startDate, new Date());
  },
};

/**
 * Các utility functions
 */
export const Utils = {
  // Chuyển đổi timestamp sang Date
  timestampToDate(timestamp) {
    return new Date(timestamp * 1000);
  },

  // Chuyển đổi Date sang timestamp
  dateToTimestamp(date) {
    return Math.floor(date.getTime() / 1000);
  },

  // Format IP address
  formatIP(ip) {
    if (!ip) return null;
    return ip.trim();
  },

  // Validate email
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate phone
  isValidPhone(phone) {
    const phoneRegex = /^[+]?[\d\s-()]+$/;
    return phoneRegex.test(phone);
  },

  // Sanitize string
  sanitizeString(str) {
    if (!str) return null;
    return str.trim().replace(/[<>]/g, '');
  },

  // Build search conditions
  buildSearchConditions(searchTerm, fields) {
    if (!searchTerm || !fields.length) return {};

    const conditions = [];
    fields.forEach((field) => {
      conditions.push({
        [field]: { $like: `%${searchTerm}%` },
      });
    });

    return { $or: conditions };
  },

  // Merge query conditions
  mergeConditions(...conditionObjects) {
    const merged = {};
    conditionObjects.forEach((obj) => {
      if (obj && obj.where) {
        Object.assign(merged, obj.where);
      }
    });
    return { where: merged };
  },
};
