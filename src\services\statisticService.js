import moment from 'moment';
import logger from '../config/logger.js';
import UserModel from '../models/mysql2/userModel.js'; // MySQL2 User model
import MySQLService from './mysqlService.js';
import { Enums } from '../models/helpers.js';
import { TIME_PERIODS, DATA_TYPES, ERROR_MESSAGES } from '../config/constants.js';

// Re-export constants for backward compatibility
export { TIME_PERIODS, DATA_TYPES };

/**
 * Fetches and returns overview statistics including accounts, unique IPs,
 * revenue, and successful transactions
 */
export const getOverview = async () => {
  try {
    const [totalAccounts, distinctIPs, transactionStats] = await Promise.all([
      UserModel.estimatedDocumentCount(),
      UserModel.distinct('ip'),
      MySQLService.query(
        'SELECT COALESCE(SUM(amount), 0) AS totalRevenue, COUNT(*) AS totalTransactions FROM transactions WHERE status = ?',
        [Enums.STATUS.COMPLETED]
      ),
    ]);

    const [transactionRow] = transactionStats;
    const validIPs = distinctIPs.filter((ip) => ip && ip !== '');

    return {
      totalAccounts: Number(totalAccounts) || 0,
      totalUniqueIPs: validIPs.length,
      totalRevenue: Number(transactionRow && transactionRow.totalRevenue) || 0,
      totalSuccessfulTransactions: Number(transactionRow && transactionRow.totalTransactions) || 0,
    };
  } catch (error) {
    logger.error('statisticService.getOverview error:', error);
    throw new Error(`Failed to get overview statistics: ${error.message}`);
  }
};

/**
 * Tính toán khoảng thời gian trước đó dựa trên khoảng thời gian hiện tại
 * @param {string} startDate - Ngày bắt đầu (YYYY-MM-DD)
 * @param {string} endDate - Ngày kết thúc (YYYY-MM-DD)
 * @param {number} timePeriod - Loại khoảng thời gian (1-5)
 * @returns {Object} Đối tượng chứa previousStartDate và previousEndDate (YYYY-MM-DD)
 */
const getPreviousDateRange = (startDate, endDate, timePeriod) => {
  const startMoment = moment(startDate);
  const endMoment = moment(endDate);
  const getDaysDiff = () => endMoment.diff(startMoment, 'days') + 1;

  const periodHandlers = {
    [TIME_PERIODS.TODAY]: () => ({
      previousStartDate: moment(startDate).subtract(1, 'day'),
      previousEndDate: moment(endDate).subtract(1, 'day'),
    }),

    [TIME_PERIODS.YESTERDAY]: () => ({
      previousStartDate: moment(startDate).subtract(2, 'days'),
      previousEndDate: moment(endDate).subtract(2, 'days'),
    }),

    [TIME_PERIODS.THIS_WEEK]: () => ({
      previousStartDate: moment(startDate).subtract(7, 'days'),
      previousEndDate: moment(endDate).subtract(7, 'days'),
    }),

    [TIME_PERIODS.LAST_7_DAYS]: () => ({
      previousStartDate: moment(startDate).subtract(7, 'days'),
      previousEndDate: moment(endDate).subtract(7, 'days'),
    }),

    [TIME_PERIODS.THIS_MONTH]: () => {
      const lastMonth = moment(startDate).subtract(1, 'months');
      return {
        previousStartDate: lastMonth.clone().startOf('month'),
        previousEndDate: lastMonth.clone().endOf('month'),
      };
    },

    default: () => {
      const daysDiff = getDaysDiff();
      return {
        previousStartDate: moment(startDate).subtract(daysDiff, 'days'),
        previousEndDate: moment(endDate).subtract(daysDiff, 'days'),
      };
    },
  };

  // Use a whitelist of allowed timePeriod values
  const validTimePeriods = [1, 2, 3, 4, 5];
  const handler = validTimePeriods.includes(timePeriod)
    ? periodHandlers[timePeriod] // eslint-disable-line security/detect-object-injection
    : periodHandlers.default;

  const { previousStartDate, previousEndDate } = handler();

  return {
    previousStartDate: previousStartDate.format('YYYY-MM-DD'),
    previousEndDate: previousEndDate.format('YYYY-MM-DD'),
  };
};

/**
 * Get overview statistics by date range and type with growth percentage
 * @param {Object} params - Parameters object
 * @param {string} params.start_date - Start date in YYYY-MM-DD format
 * @param {string} params.end_date - End date in YYYY-MM-DD format
 * @param {number} params.type - Data type (1=Accounts, 2=IPs, 3=Revenue, 4=Transactions)
 * @param {number} params.time_period - Time period (1=Today, 2=Yesterday, 3=This week, 4=Last 7 days, 5=This month)
 * @returns {Promise<Object>} Statistics result with current value, previous value, and growth percentage
 */
/**
 * Tính toán phần trăm tăng trưởng giữa hai giá trị
 * @param {number} currentValue - Giá trị hiện tại
 * @param {number} previousValue - Giá trị trước đó
 * @returns {Object} Đối tượng chứa:
 *   - value: phần trăm tăng trưởng (đã làm tròn 2 chữ số)
 *   - isGrowth: boolean cho biết có tăng trưởng không
 */
const calculateGrowth = (currentValue, previousValue) => {
  // Xử lý trường hợp mẫu số bằng 0
  if (previousValue === 0) {
    return {
      value: currentValue > 0 ? 100 : 0,
      isGrowth: currentValue > 0,
    };
  }

  const growth = ((currentValue - previousValue) / previousValue) * 100;
  const roundedGrowth = Math.round(growth * 100) / 100;

  return {
    value: roundedGrowth,
    isGrowth: growth >= 0,
  };
};

/**
 * Lấy tổng quan thống kê theo ngày với phần trăm tăng trưởng
 * @param {Object} params - Đối tượng chứa các tham số
 * @param {string} params.start_date - Ngày bắt đầu (YYYY-MM-DD)
 * @param {string} params.end_date - Ngày kết thúc (YYYY-MM-DD)
 * @param {number} params.type - Loại dữ liệu (1-4)
 * @param {number} params.time_period - Khoảng thời gian (1-5)
 * @returns {Promise<Object>} Kết quả thống kê
 * @throws {Error} Khi có lỗi xảy ra
 */
export const getOverviewByDate = async (params) => {
  try {
    const { start_date, end_date, type, time_period = TIME_PERIODS.TODAY } = params;
    const currentValue = await getValueByType(type, start_date, end_date);
    const { previousStartDate, previousEndDate } = getPreviousDateRange(
      start_date,
      end_date,
      time_period
    );

    const previousValue = await getValueByType(type, previousStartDate, previousEndDate);

    // Tính toán phần trăm tăng trưởng
    const { value: growthPercentage, isGrowth } = calculateGrowth(currentValue, previousValue);

    return {
      total: currentValue,
      previousValue,
      growthPercentage,
      isGrowth,
    };
  } catch (error) {
    logger.error('Lỗi khi lấy tổng quan thống kê:', error);
    throw new Error(`Không thể lấy dữ liệu thống kê: ${error.message}`);
  }
};

/**
 * Lấy dữ liệu thống kê tài khoản trong khoảng thời gian
 * @param {number} startTimestamp - Timestamp bắt đầu
 * @param {number} endTimestamp - Timestamp kết thúc
 * @returns {Promise<number>} Số lượng tài khoản
 */
const getAccountCount = async (startTimestamp, endTimestamp) =>
  UserModel.countDocuments({
    createTime: { $gte: startTimestamp, $lte: endTimestamp },
  });

/**
 * Lấy dữ liệu thống kê địa chỉ IP duy nhất
 * @param {number} startTimestamp - Timestamp bắt đầu
 * @param {number} endTimestamp - Timestamp kết thúc
 * @returns {Promise<number>} Số lượng IP duy nhất
 */
const getUniqueIPCount = async (startTimestamp, endTimestamp) => {
  const distinctIPs = await UserModel.distinct('ip', {
    createTime: { $gte: startTimestamp, $lte: endTimestamp },
    ip: { $exists: true, $nin: [null, ''] },
  });
  return distinctIPs.length;
};

/**
 * Lấy tổng doanh thu trong khoảng thời gian
 * @param {string} startDate - Ngày bắt đầu (YYYY-MM-DD)
 * @param {string} endDate - Ngày kết thúc (YYYY-MM-DD)
 * @returns {Promise<number>} Tổng doanh thu
 */
const getTotalRevenue = async (startDate, endDate) => {
  const [revenueResult] = await MySQLService.query(
    `SELECT COALESCE(SUM(amount), 0) AS total
     FROM transactions
     WHERE status = ? AND created_at BETWEEN ? AND ?`,
    [Enums.STATUS.COMPLETED, startDate, endDate]
  );
  return Number(revenueResult?.total) || 0;
};

/**
 * Lấy tổng số giao dịch trong khoảng thời gian
 * @param {string} startDate - Ngày bắt đầu (YYYY-MM-DD)
 * @param {string} endDate - Ngày kết thúc (YYYY-MM-DD)
 * @returns {Promise<number>} Tổng số giao dịch
 */
const getTransactionCount = async (startDate, endDate) => {
  const [transactionResult] = await MySQLService.query(
    `SELECT COUNT(*) AS total
     FROM transactions
     WHERE status = ? AND created_at BETWEEN ? AND ?`,
    [Enums.STATUS.COMPLETED, startDate, endDate]
  );
  return Number(transactionResult?.total) || 0;
};

/**
 * Lấy giá trị thống kê theo loại dữ liệu
 * @param {number} type - Loại dữ liệu (1-4)
 * @param {string} startDate - Ngày bắt đầu (YYYY-MM-DD)
 * @param {string} endDate - Ngày kết thúc (YYYY-MM-DD)
 * @returns {Promise<number>} Giá trị thống kê
 */
async function getValueByType(type, startDate, endDate) {
  const startMoment = moment(startDate).startOf('day');
  const endMoment = moment(endDate).endOf('day');
  const startTimestamp = startMoment.unix();
  const endTimestamp = endMoment.unix();

  // Define valid data types and their corresponding handlers
  const validTypeHandlers = {
    [DATA_TYPES.ACCOUNTS]: () => getAccountCount(startTimestamp, endTimestamp),
    [DATA_TYPES.IPS]: () => getUniqueIPCount(startTimestamp, endTimestamp),
    [DATA_TYPES.REVENUE]: () => getTotalRevenue(startDate, endDate),
    [DATA_TYPES.TRANSACTIONS]: () => getTransactionCount(startDate, endDate),
  };

  // Use a whitelist approach to prevent object injection
  const handler = validTypeHandlers[type]; // eslint-disable-line security/detect-object-injection
  if (typeof handler !== 'function') {
    throw new Error(ERROR_MESSAGES.INVALID_DATA_TYPE);
  }

  return await handler();
}

/**
 * Get daily statistics for account registrations and IP addresses
 * @param {Object} params - Parameters object
 * @param {string} params.start_date - Start date in YYYY-MM-DD format
 * @param {string} params.end_date - End date in YYYY-MM-DD format
 * @param {number|null} params.type - Filter by type (1=Accounts only, 2=IPs only, null=Both)
 * @returns {Promise<Object>} Daily statistics result
 */
export const getDailyStatistics = async (params) => {
  try {
    const { start_date, end_date, type } = params;

    // Convert dates using moment
    const startMoment = moment(start_date);
    const endMoment = moment(end_date);

    // Generate date range array using moment
    const dateRange = [];
    const currentMoment = moment(startMoment);
    while (currentMoment.isSameOrBefore(endMoment)) {
      dateRange.push(currentMoment.format('YYYY-MM-DD'));
      currentMoment.add(1, 'day');
    }

    // Get daily statistics
    const dailyStats = [];
    let totalAccounts = 0;
    let totalUniqueIPs = 0;
    const allIPs = new Set();

    for (const date of dateRange) {
      const dayStart = moment(date).startOf('day').unix();
      const dayEnd = moment(date).endOf('day').unix();

      const dayStats = { date };

      if (!type || type === 1) {
        // Get accounts for this day
        const accountCount = await UserModel.countDocuments({
          createTime: { $gte: dayStart, $lte: dayEnd },
        });
        dayStats.registered_accounts = accountCount;
        totalAccounts += accountCount;
      }

      if (!type || type === 2) {
        // Get unique IPs for this day
        const dayIPs = await UserModel.distinct('ip', {
          createTime: { $gte: dayStart, $lte: dayEnd },
          ip: { $exists: true, $nin: [null, ''] },
        });
        dayStats.registered_ips = dayIPs.length;
        dayIPs.forEach((ip) => allIPs.add(ip));
      }

      // Calculate clone coefficient if both accounts and IPs are present
      if (dayStats.registered_accounts && dayStats.registered_ips && dayStats.registered_ips > 0) {
        dayStats.clone_coefficient = Number(
          (dayStats.registered_accounts / dayStats.registered_ips).toFixed(3)
        );
      }

      dailyStats.push(dayStats);
    }

    totalUniqueIPs = allIPs.size;

    // Build response based on type filter
    const response = { daily_stats: dailyStats };

    if (!type) {
      // Both accounts and IPs
      response.total_summary = {
        total_registered_accounts: totalAccounts,
        total_registered_ips: totalUniqueIPs,
      };
    } else if (type === 1) {
      // Accounts only
      response.total_summary = {
        total_registered_accounts: totalAccounts,
      };
    } else if (type === 2) {
      // IPs only
      response.total_summary = {
        total_registered_ips: totalUniqueIPs,
      };
    }

    return response;
  } catch (error) {
    logger.error('statisticService.getDailyStatistics error:', error);
    throw new Error(`Failed to get daily statistics: ${error.message}`);
  }
};

/**
 * Get revenue statistics for a specified date range
 * @param {Object} params - Parameters object
 * @param {string} params.start_date - Start date in YYYY-MM-DD format
 * @param {string} params.end_date - End date in YYYY-MM-DD format
 * @returns {Promise<Object>} Revenue statistics result
 */
export const getRevenueStatistics = async (params) => {
  try {
    const { start_date, end_date } = params;

    // Get daily revenue data
    const revenueResult = await MySQLService.query(
      `SELECT 
         DATE(created_at) as date,
         COALESCE(SUM(amount), 0) as revenue
       FROM transactions 
       WHERE status = ? AND DATE(created_at) BETWEEN ? AND ?
       GROUP BY DATE(created_at)
       ORDER BY DATE(created_at)`,
      [Enums.STATUS.COMPLETED, start_date, end_date]
    );

    // Get total revenue for the period
    const totalResult = await MySQLService.query(
      'SELECT COALESCE(SUM(amount), 0) AS totalRevenue FROM transactions WHERE status = ? AND DATE(created_at) BETWEEN ? AND ?',
      [Enums.STATUS.COMPLETED, start_date, end_date]
    );

    // Format daily revenue data
    const dailyRevenue = revenueResult.map((row) => ({
      date: row.date,
      revenue: Number(row.revenue) || 0,
    }));

    return {
      daily_revenue: dailyRevenue,
      total_revenue: Number(totalResult[0]?.totalRevenue) || 0,
    };
  } catch (error) {
    logger.error('statisticService.getRevenueStatistics error:', error);
    throw new Error(`Failed to get revenue statistics: ${error.message}`);
  }
};
