import { Sequelize } from 'sequelize';

// Import base model and helpers
import BaseModel from './BaseModel.js';
import { Enums, Fields, Queries, Utils } from './helpers.js';

// Import MySQL models (Sequelize)
import Transaction from './mysql/Transaction.js';
import HistoryTransaction from './mysql/HistoryTransaction.js';
import McoinTransaction from './mysql/McoinTransaction.js';
import AccountWallet from './mysql/AccountWallet.js';
import AccountCard from './mysql/AccountCard.js';
import AccountLog from './mysql/AccountLog.js';

// Import MySQL2 models
import UserModel from './mysql2/userModel.js';
import ProfileModel from './mysql2/profileModel.js';
import SignUpSourceModel from './mysql2/signUpSourceModel.js';

/**
 * Initialize MySQL models with Sequelize
 * @param {Sequelize} sequelize - Sequelize instance
 * @returns {Object} Object containing all initialized MySQL models
 */
export function initializeMySQLModels(sequelize) {
  const { DataTypes } = Sequelize;

  try {
    // Initialize all MySQL models
    const models = {
      Transaction: Transaction.init(sequelize, DataTypes),
      HistoryTransaction: HistoryTransaction.init(sequelize, DataTypes),
      McoinTransaction: McoinTransaction.init(sequelize, DataTypes),
      AccountWallet: AccountWallet.init(sequelize, DataTypes),
      AccountCard: AccountCard.init(sequelize, DataTypes),
      AccountLog: AccountLog.init(sequelize, DataTypes),
    };

    // Set up associations
    Object.values(models).forEach((model) => {
      if (model.associate) {
        model.associate(models);
      }
    });

    console.log('✅ All MySQL models initialized successfully');
    return models;
  } catch (error) {
    console.error('❌ Error initializing MySQL models:', error.message);
    throw new Error(`Failed to initialize MySQL models: ${error.message}`);
  }
}

/**
 * Initialize MySQL2 models (không cần Sequelize)
 * @returns {Object} Object containing all MySQL2 model classes
 */
export function initializeMySQL2Models() {
  try {
    // MySQL2 models không cần initialize như Sequelize
    const models = {
      UserModel,
      ProfileModel,
      SignUpSourceModel,
    };

    // Set up associations nếu có
    Object.values(models).forEach((model) => {
      if (model.associate) {
        model.associate(models);
      }
    });

    console.log('✅ All MySQL2 models initialized successfully');
    return models;
  } catch (error) {
    console.error('❌ Error initializing MySQL2 models:', error.message);
    throw new Error(`Failed to initialize MySQL2 models: ${error.message}`);
  }
}

/**
 * Initialize all models (cả MySQL và MySQL2)
 * @param {Sequelize} sequelize - Sequelize instance (optional, chỉ cho MySQL)
 * @returns {Object} Object containing all models
 */
export function initializeAllModels(sequelize = null) {
  try {
    const allModels = {
      mysql: sequelize ? initializeMySQLModels(sequelize) : {},
      mysql2: initializeMySQL2Models(),
    };

    console.log('✅ All models (MySQL + MySQL2) initialized successfully');
    return allModels;
  } catch (error) {
    console.error('❌ Error initializing all models:', error.message);
    throw new Error(`Failed to initialize all models: ${error.message}`);
  }
}

/**
 * Sync MySQL models with database
 * @param {Sequelize} sequelize - Sequelize instance
 * @param {Object} options - Sync options
 */
export async function syncMySQLModels(sequelize, options = {}) {
  try {
    const models = initializeMySQLModels(sequelize);

    const syncOptions = Object.assign(
      {
        force: false, // Set to true to drop and recreate tables
        alter: false, // Set to true to alter existing tables
      },
      options
    );

    await sequelize.sync(syncOptions);
    console.log('✅ All MySQL models synchronized with database');

    return models;
  } catch (error) {
    console.error('❌ Error syncing MySQL models:', error.message);
    throw new Error(`Failed to sync MySQL models: ${error.message}`);
  }
}

/**
 * Get all MySQL model classes (not initialized)
 * @returns {Object} Object containing all MySQL model classes
 */
export function getMySQLModelClasses() {
  return {
    BaseModel,
    Transaction,
    HistoryTransaction,
    McoinTransaction,
    AccountWallet,
    AccountCard,
    AccountLog,
  };
}

/**
 * Get all MySQL2 model classes
 * @returns {Object} Object containing all MySQL2 model classes
 */
export function getMySQL2ModelClasses() {
  return {
    BaseModel,
    UserModel,
    ProfileModel,
    SignUpSourceModel,
  };
}

/**
 * Get all model classes (cả MySQL và MySQL2)
 * @returns {Object} Object containing all model classes
 */
export function getAllModelClasses() {
  return {
    mysql: getMySQLModelClasses(),
    mysql2: getMySQL2ModelClasses(),
  };
}

/**
 * Validate model associations
 * @param {Object} models - Initialized models
 * @param {string} type - 'mysql' hoặc 'mysql2'
 * @returns {boolean} Validation result
 */
export function validateAssociations(models, type = 'mysql') {
  try {
    const modelEntries = Object.entries(models);
    console.log(`🔍 Validating ${type} associations for ${modelEntries.length} models...`);

    if (type === 'mysql') {
      // Check Sequelize associations
      for (const [modelName, model] of modelEntries) {
        if (model && model.associations) {
          console.log(`  ✅ ${modelName}: ${Object.keys(model.associations).length} associations`);
        } else {
          console.log(`  ⚠️  ${modelName}: No associations defined`);
        }
      }
    } else {
      // Check MySQL2 model structure
      for (const [modelName, model] of modelEntries) {
        if (model && model.tableName) {
          console.log(`  ✅ ${modelName}: table '${model.tableName}'`);
        } else {
          console.log(`  ⚠️  ${modelName}: No tableName defined`);
        }
      }
    }

    return true;
  } catch (error) {
    console.error(`❌ Error validating ${type} associations:`, error.message);
    return false;
  }
}

/**
 * Get model statistics
 * @param {Object} models - Initialized models
 * @param {string} type - 'mysql' hoặc 'mysql2'
 * @returns {Object} Model statistics
 */
export async function getModelStats(models, type = 'mysql') {
  try {
    const stats = {};

    for (const [modelName, model] of Object.entries(models)) {
      try {
        if (type === 'mysql' && model.count) {
          const count = await model.count();
          Object.defineProperty(stats, modelName, {
            value: {
              recordCount: count,
              tableName: model.tableName,
              associations: Object.keys(model.associations || {}).length,
            },
            writable: true,
            enumerable: true,
          });
        } else {
          Object.defineProperty(stats, modelName, {
            value: {
              tableName: model.tableName || 'N/A',
              type: type,
            },
            writable: true,
            enumerable: true,
          });
        }
      } catch (error) {
        Object.defineProperty(stats, modelName, {
          value: {
            error: error.message,
            tableName: model.tableName || 'N/A',
          },
          writable: true,
          enumerable: true,
        });
      }
    }

    return stats;
  } catch (error) {
    console.error(`❌ Error getting ${type} model stats:`, error.message);
    throw new Error(`Failed to get ${type} model stats: ${error.message}`);
  }
}

/**
 * Test database connection and models
 * @param {Sequelize} sequelize - Sequelize instance (optional)
 * @returns {Promise<boolean>} Test result
 */
export async function testConnection(sequelize = null) {
  try {
    console.log('🔍 Testing database connections and models...');

    if (sequelize) {
      // Test MySQL connection
      await sequelize.authenticate();
      console.log('✅ MySQL database connection successful');

      // Initialize MySQL models
      const mysqlModels = initializeMySQLModels(sequelize);

      // Validate MySQL associations
      validateAssociations(mysqlModels, 'mysql');

      // Get MySQL model stats
      const mysqlStats = await getModelStats(mysqlModels, 'mysql');
      console.log('📊 MySQL model statistics:', mysqlStats);
    }

    // Initialize MySQL2 models
    const mysql2Models = initializeMySQL2Models();

    // Validate MySQL2 associations
    validateAssociations(mysql2Models, 'mysql2');

    // Get MySQL2 model stats
    const mysql2Stats = await getModelStats(mysql2Models, 'mysql2');
    console.log('📊 MySQL2 model statistics:', mysql2Stats);

    return true;
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    return false;
  }
}

// Export base model and helpers
export { BaseModel, Enums, Fields, Queries, Utils };

// Export MySQL model classes
export {
  Transaction,
  HistoryTransaction,
  McoinTransaction,
  AccountWallet,
  AccountCard,
  AccountLog,
};

// Export MySQL2 model classes
export {
  UserModel,
  ProfileModel,
  SignUpSourceModel,
};
