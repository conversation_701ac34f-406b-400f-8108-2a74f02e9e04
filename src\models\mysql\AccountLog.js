import BaseModel from '../BaseModel.js';
import { Enums, Fields, Queries } from '../helpers.js';

/**
 * Model Nhật ký tà<PERSON> k<PERSON>n
 */
export default class AccountLog extends BaseModel {
  static get tableName() {
    return 'account_logs';
  }

  static getFields(DataTypes) {
    return Object.assign({}, super.getFields(DataTypes), {
      accountId: Fields.accountId(DataTypes),
      accountName: Fields.accountName(DataTypes),

      action: {
        type: DataTypes.TINYINT,
        allowNull: false,
        comment:
          'Hành động: 0=Không xác định, 1=<PERSON><PERSON>ng nhập, 2=<PERSON><PERSON>ng xuất, 3=<PERSON><PERSON><PERSON> d<PERSON>ch, 4=<PERSON><PERSON><PERSON> nhật hồ sơ, 5=Thay đổi bảo mật',
        validate: { isIn: [[0, 1, 2, 3, 4, 5]] },
      },

      ipAddress: {
        type: DataTypes.STRING(45),
        allowNull: true,
        field: 'ip_address',
        comment: 'Địa chỉ IP',
      },

      userAgent: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'user_agent',
        comment: 'Thông tin trình duyệt',
      },

      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Mô tả hành động',
      },

      metaData: Fields.metaData(DataTypes),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['account_id'] },
        { fields: ['action'] },
        { fields: ['ip_address'] },
        { fields: ['created_at'] },
        { fields: ['account_id', 'action'] },
        { fields: ['account_id', 'created_at'] },
      ],
    });
  }

  static getActionEnum() {
    return Enums.ACTION;
  }

  // Tìm kiếm cơ bản
  static async findLogins() {
    return await this.findAll({ where: { action: this.getActionEnum().LOGIN } });
  }

  static async findByAccount(accountId) {
    return await this.findAll(Queries.byAccount(accountId));
  }

  static async findToday() {
    return await this.findAll(Queries.today());
  }

  static async findThisMonth() {
    return await this.findAll(Queries.thisMonth());
  }

  // Thống kê cơ bản
  static async countToday() {
    return await this.count(Queries.today());
  }

  static async countByAction(action) {
    return await this.count({ where: { action } });
  }

  // Tạo log cơ bản
  static async logLogin(
    accountId,
    accountName,
    ipAddress,
    userAgent,
    description = 'Đăng nhập thành công'
  ) {
    return await this.create({
      accountId,
      accountName,
      action: this.getActionEnum().LOGIN,
      ipAddress,
      userAgent,
      description,
    });
  }

  static async logTransaction(accountId, accountName, description, metaData = null) {
    return await this.create({
      accountId,
      accountName,
      action: this.getActionEnum().TRANSACTION,
      description,
      metaData,
    });
  }
}
