import { Model } from 'sequelize';

/**
 * Base Model - Model cơ bản cho tất cả các bảng
 * Hỗ trợ cả Sequelize (MySQL) và MySQL2
 */
export default class BaseModel extends Model {
  /**
   * Khởi tạo model với Sequelize (cho MySQL)
   */
  static init(sequelize, DataTypes) {
    const defaultOptions = {
      sequelize: sequelize,
      modelName: this.name,
      tableName: this.tableName,
      timestamps: true,
      paranoid: true,
      underscored: true,
      freezeTableName: true,
    };

    const customOptions = this.getOptions() || {};
    const options = Object.assign({}, defaultOptions, customOptions);

    return super.init(this.getFields(DataTypes), options);
  }

  /**
   * Tên bảng - override trong class con (cho MySQL2)
   */
  static get tableName() {
    throw new Error('tableName must be defined in child class');
  }

  /**
   * Đ<PERSON><PERSON> nghĩa các field cơ bản cho Sequelize
   */
  static getFields(DataTypes) {
    if (!DataTypes) {
      // Trường hợp MySQL2 - trả về field names
      return this.getBaseFields();
    }

    // Trường hợp Sequelize - trả về field definitions
    return {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: 'ID chính',
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'created_at',
        comment: 'Thời gian tạo',
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'updated_at',
        comment: 'Thời gian cập nhật',
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'deleted_at',
        comment: 'Thời gian xóa',
      },
    };
  }

  /**
   * Các field cơ bản cho tất cả bảng (cho MySQL2)
   */
  static getBaseFields() {
    return ['id', 'created_at', 'updated_at', 'deleted_at'];
  }

  /**
   * Cấu hình tùy chọn
   */
  static getOptions() {
    return {
      indexes: [{ fields: ['created_at'] }],
      tableOptions: {
        engine: 'InnoDB',
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
      },
    };
  }

  /**
   * Lấy enum status - override trong class con nếu cần
   */
  static getStatusEnum() {
    return {};
  }

  /**
   * Associations - override trong class con nếu cần
   */
  static associate() {
    // Override in child classes if needed
  }

  /**
   * Validation dữ liệu cơ bản
   * @param {Object} data - Dữ liệu cần validate
   */
  static validate(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('Data must be an object');
    }
    return true;
  }

  /**
   * Transform dữ liệu từ database
   * @param {Object} row - Dữ liệu từ DB
   */
  static transform(row) {
    if (!row) return null;

    // Có thể override trong class con để transform data
    return row;
  }

  /**
   * Tìm theo ID (cho Sequelize)
   */
  static async findById(id, options = {}) {
    if (!this.findByPk) {
      throw new Error('findById method is only available for Sequelize models');
    }
    
    const record = await this.findByPk(id, options);
    if (!record) {
      throw new Error(`${this.name} với ID ${id} không tồn tại`);
    }
    return record;
  }

  /**
   * Tạo record mới (cho Sequelize)
   */
  static async create(data, options = {}) {
    if (!super.create) {
      throw new Error('create method is only available for Sequelize models');
    }
    
    try {
      return await super.create(data, options);
    } catch (error) {
      throw new Error(`Lỗi tạo ${this.name}: ${error.message}`);
    }
  }

  /**
   * Cập nhật record (cho Sequelize)
   */
  static async update(data, options = {}) {
    if (!super.update) {
      throw new Error('update method is only available for Sequelize models');
    }
    
    try {
      return await super.update(data, options);
    } catch (error) {
      throw new Error(`Lỗi cập nhật ${this.name}: ${error.message}`);
    }
  }

  /**
   * Xóa record (cho Sequelize)
   */
  static async delete(options = {}) {
    if (!this.destroy) {
      throw new Error('delete method is only available for Sequelize models');
    }
    
    try {
      return await this.destroy(options);
    } catch (error) {
      throw new Error(`Lỗi xóa ${this.name}: ${error.message}`);
    }
  }

  /**
   * Đếm số lượng record (cho Sequelize)
   */
  static async count(options = {}) {
    if (!super.count) {
      throw new Error('count method is only available for Sequelize models');
    }
    
    try {
      return await super.count(options);
    } catch (error) {
      throw new Error(`Lỗi đếm ${this.name}: ${error.message}`);
    }
  }

  /**
   * Tìm tất cả record (cho Sequelize)
   */
  static async findAll(options = {}) {
    if (!super.findAll) {
      throw new Error('findAll method is only available for Sequelize models');
    }
    
    try {
      return await super.findAll(options);
    } catch (error) {
      throw new Error(`Lỗi tìm ${this.name}: ${error.message}`);
    }
  }

  /**
   * Tìm một record (cho Sequelize)
   */
  static async findOne(options = {}) {
    if (!super.findOne) {
      throw new Error('findOne method is only available for Sequelize models');
    }
    
    try {
      return await super.findOne(options);
    } catch (error) {
      throw new Error(`Lỗi tìm ${this.name}: ${error.message}`);
    }
  }

  /**
   * Phân trang (cho Sequelize)
   */
  static async paginate(page = 1, limit = 10, options = {}) {
    if (!this.findAndCountAll) {
      throw new Error('paginate method is only available for Sequelize models');
    }
    
    const offset = (page - 1) * limit;
    const query = Object.assign({}, options, {
      limit: limit,
      offset: offset,
    });
    const { count, rows } = await this.findAndCountAll(query);

    return {
      data: rows,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1,
      },
    };
  }
}
