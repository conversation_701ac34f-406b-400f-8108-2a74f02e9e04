import BaseModel from '../BaseModel.js';
import { Enums, Fields, Queries } from '../helpers.js';

/**
 * Model Giao dịch
 */
export default class Transaction extends BaseModel {
  static get tableName() {
    return 'transactions';
  }

  static getFields(DataTypes) {
    return Object.assign({}, super.getFields(DataTypes), {
      accountId: Fields.accountId(DataTypes),
      accountName: Fields.accountName(DataTypes),

      orderId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'order_id',
        comment: 'Mã đơn hàng',
      },

      amount: Fields.amount(DataTypes),
      coinAmount: Fields.coinAmount(DataTypes),
      paymentMethod: Fields.paymentMethod(DataTypes),
      status: Fields.status(DataTypes),
      metaData: Fields.metaData(DataTypes),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['account_id'] },
        { fields: ['order_id'], unique: true },
        { fields: ['status'] },
        { fields: ['payment_method'] },
        { fields: ['created_at'] },
        { fields: ['account_id', 'status'] },
        { fields: ['account_id', 'created_at'] },
      ],
    });
  }

  static associate(models) {
    this.hasOne(models.HistoryTransaction, {
      foreignKey: 'transactionId',
      as: 'historyTransaction',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }

  static getStatusEnum() {
    return Enums.STATUS;
  }

  static getPaymentMethodEnum() {
    return Enums.PAYMENT_METHOD;
  }

  // Tìm kiếm cơ bản
  static async findPending() {
    return await this.findAll(Queries.byStatus(Enums.STATUS.PENDING));
  }

  static async findCompleted() {
    return await this.findAll(Queries.byStatus(Enums.STATUS.COMPLETED));
  }

  static async findByAccount(accountId) {
    return await this.findAll(Queries.byAccount(accountId));
  }

  static async findToday() {
    return await this.findAll(Queries.today());
  }

  static async findThisMonth() {
    return await this.findAll(Queries.thisMonth());
  }

  // Thống kê cơ bản
  static async countByStatus(status) {
    return await this.count(Queries.byStatus(status));
  }

  static async sumAmountToday() {
    const query = Object.assign({}, Queries.today(), {
      attributes: [[this.sequelize.fn('SUM', this.sequelize.col('amount')), 'total']],
    });
    const result = await this.findOne(query);
    return result ? parseFloat(result.getDataValue('total') || 0) : 0;
  }
}
