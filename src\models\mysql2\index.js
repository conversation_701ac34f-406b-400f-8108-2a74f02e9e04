/**
 * Index file cho MySQL2 models - <PERSON><PERSON><PERSON><PERSON> tự mysql/index.js
 */
import BaseModel from './BaseModel.js';
import UserModel from './userModel.js';
import ProfileModel from './profileModel.js';
import SignUpSourceModel from './signUpSourceModel.js';

/**
 * Initialize all MySQL2 models (không cần Sequelize như MySQL)
 * @returns {Object} Object containing all model classes
 */
export function initializeModels() {
  try {
    // MySQL2 models không cần initialize như Sequelize
    const models = {
      UserModel,
      ProfileModel,
      SignUpSourceModel,
    };

    // Set up associations nếu có
    Object.values(models).forEach((model) => {
      if (model.associate) {
        model.associate(models);
      }
    });

    console.log('✅ All MySQL2 models initialized successfully');
    return models;
  } catch (error) {
    console.error('❌ Error initializing MySQL2 models:', error.message);
    throw new Error(`Failed to initialize MySQL2 models: ${error.message}`);
  }
}

/**
 * Get all model classes
 * @returns {Object} Object containing all model classes
 */
export function getModelClasses() {
  return {
    BaseModel,
    UserModel,
    ProfileModel,
    SignUpSourceModel,
  };
}

/**
 * Validate model associations
 * @param {Object} models - Model classes
 * @returns {boolean} Validation result
 */
export function validateAssociations(models) {
  try {
    const modelEntries = Object.entries(models);
    console.log(`🔍 Validating MySQL2 associations for ${modelEntries.length} models...`);

    // Check if all models have proper structure
    for (const [modelName, model] of modelEntries) {
      if (model && model.tableName) {
        console.log(`  ✅ ${modelName}: table '${model.tableName}'`);
      } else {
        console.log(`  ⚠️  ${modelName}: No tableName defined`);
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Error validating MySQL2 associations:', error.message);
    return false;
  }
}

/**
 * Test MySQL2 models structure
 * @returns {Promise<boolean>} Test result
 */
export async function testModels() {
  try {
    console.log('🔍 Testing MySQL2 models structure...');

    // Initialize models
    const models = initializeModels();

    // Validate associations
    validateAssociations(models);

    console.log('📊 MySQL2 models structure validated successfully');

    return true;
  } catch (error) {
    console.error('❌ MySQL2 models test failed:', error.message);
    return false;
  }
}

// Export model classes
export { BaseModel, UserModel, ProfileModel, SignUpSourceModel };

// Export helpers
export * from './helpers.js';
